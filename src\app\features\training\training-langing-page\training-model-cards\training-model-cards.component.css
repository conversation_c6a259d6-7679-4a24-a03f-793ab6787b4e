.card-wrapper {
  background-color: white;
  border: 1px solid transparent;
  border-radius: 12px;
  margin-bottom: 5px;
}

.right-section {
  width: 200px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.divider-line {
  width: 1px;
  height: 100%;
  background-color: #e0e0e0;
  margin-right: 16px;
  flex-shrink: 0;
}

.status-button {
  min-width: 120px;
  padding: 12px 20px;
  border: none;
  border-radius: 15px;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  color: #333;
  cursor: pointer;
  transition: all 0.2s ease;
}

/* Status variants */
.status-button.completed {
  background-color: transparent;
}

.status-button.configured {
  background-color: #e8def8;
}

.status-button.stopped,
.status-button.failed {
  background-color: #fdd3d0;
}

.status-button.training {
  background: linear-gradient(to right, #e8def8, #ffffff);
}

.status-icon-button {
  margin-left: 8px;
  color: #9da0a7;
}

.status-icon-button .mat-icon {
  color: inherit;
}

.status {
  color: #73777f;
}

.color_gray {
  color: #ababa9;
}
