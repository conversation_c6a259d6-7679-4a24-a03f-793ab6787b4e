.card-wrapper {
  background-color: white;
  border: 1px;
  border-radius: 12px;
  margin-bottom: 5px;
}
.status {
  color: #73777f;
}
/* Fixed width right section */
.right-section {
  width: 200px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

/* Vertical divider line */
.divider-line {
  width: 1px;
  height: 40px;
  background-color: #e0e0e0;
  margin-right: 16px;
  flex-shrink: 0;
}

/* Base status button styling */
.status-button {
  min-width: 120px;
  padding: 12px 20px;
  border-radius: 15px;
  cursor: pointer;
  border: none;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  transition: all 0.2s ease;
}

/* Status-specific styling */
.status-button.completed {
  background-color: transparent;
  color: #333;
}

.status-button.configured {
  background-color: #e8def8;
  color: #333;
}

.status-button.stopped {
  background-color: #fdd3d0;
  color: #333;
}

.status-button.training {
  background: linear-gradient(to right, #e8def8, #ffffff);
  color: #333;
}

.status-button.failed {
  background-color: #fdd3d0;
  color: #333;
}

/* Icon button styling */
.status-icon-button {
  margin-left: 8px;
  color: #9da0a7;
}

.status-icon-button .mat-icon {
  color: #9da0a7;
}
.color_gray {
  color: rgb(171, 169, 169);
}
