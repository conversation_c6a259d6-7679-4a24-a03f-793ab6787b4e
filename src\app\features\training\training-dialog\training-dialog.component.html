<div class="dialog-wrapper">
  <div class="flex justify-between items-center object-center pb-6">
    <h2 mat-dialog-title class="text-xl font-semibold">New Training</h2>
    <button
      mat-icon-button
      (click)="onNoClick()"
      class="text-gray-400 hover:text-gray-600">
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <mat-dialog-content>
    <mat-stepper #stepper>
      <mat-step>
        <form [formGroup]="trainingForm" class="form-wrapper">
          <div class="flex flex-col flex-grow space-y-6">
            <div>
              <label for="name" class="mb-3 block font-medium"
                >Name<span class="text-red-500">*</span></label
              >
              <input type="text" formControlName="name" class="form-input w-full" />
            </div>

            <div>
              <label for="trainingData" class="mb-3 block font-medium"
                >Training Data<span class="text-red-500">*</span></label
              >
              <app-select-file-folder
                [preselectedFile]="preselectedFile"
                (selectedFile)="getSelectedFile($event)"></app-select-file-folder>
            </div>

            <div>
              <label for="datasetVersion" class="mb-3 block font-medium">Dataset Version</label>
              <mat-select
                formControlName="datasetVersion"
                class="select-menu flex items-center pl-2 pr-2 w-full">
                <mat-option value="Version 1">Version 1</mat-option>
              </mat-select>
            </div>

            <div>
              <label for="analysisGoal" class="mb-3 block font-medium"
                >Analysis Goal<span class="text-red-500">*</span></label
              >
              <div class="w-full flex flex-row gap-4">
                <mat-select
                  formControlName="analysisGoalName"
                  (selectionChange)="setSelectedValue($event)"
                  class="select-menu flex items-center pl-2 pr-2 w-1/3">
                  @for (task of mlTasks; track task) {
                    <mat-option [value]="task">{{ task }}</mat-option>
                  }
                </mat-select>
                <mat-select
                  formControlName="anaysisGoalType"
                  class="select-menu flex items-center pl-2 pr-2 w-2/3">
                  @for (model of mlModelNames; track model) {
                    <mat-option [value]="model.id">{{ model.name }}</mat-option>
                  }
                </mat-select>
              </div>
            </div>
          </div>

          <div class="flex justify-between items-center dialog-actions">
            <button mat-button class="bg-white" (click)="onNoClick()">
              Cancel
            </button>
            <span class="text-sm text-gray-600">Step {{ stepper.selectedIndex + 1 }} of 3</span>
            <button
              mat-flat-button
              matStepperNext
              [disabled]="checkStepper1FormError()">
              Next
            </button>
          </div>
        </form>
      </mat-step>
      <mat-step>
        <form [formGroup]="trainingForm" class="form-wrapper">
          <div class="flex-grow space-y-6">
            <div>
              <label for="datasetVersion" class="mb-3 block font-medium"
                >Target<span class="text-red-500">*</span></label
              >
              <mat-select
                formControlName="selectedTarget"
                (selectionChange)="selectedTarget($event)"
                class="select-menu flex items-center pl-2 pr-2 w-full">
                @for (target of targetColumnInfo; track target) {
                  <mat-option [value]="target.id">{{ target.name }}</mat-option>
                }
              </mat-select>
            </div>

            <div>
              <label for="datasetVersion" class="mb-3 block font-medium"
                >Features<span class="text-red-500">*</span></label
              >
              <mat-select
                multiple
                formControlName="selectedFeatures"
                (selectionChange)="selectedFeatures($event)"
                class="select-menu flex items-center pl-2 pr-2 w-full">
                @for (feature of featureColumnInfo; track feature) {
                  <mat-option [value]="feature.id">
                    <mat-checkbox [checked]="feature.checked">
                      {{ feature.name }}
                    </mat-checkbox>
                  </mat-option>
                }
              </mat-select>
            </div>

            <div>
              <label for="datasetVersion" class="mb-3 block font-medium"
                >Dataset Split<span class="text-red-500">*</span></label
              >
              <div class="w-full flex flex-row gap-8 items-center justify-center">
                <div class="flex items-center gap-3">
                  <span class="font-medium">Train</span>
                  <input
                    type="number"
                    formControlName="train_size"
                    min="5"
                    max="95"
                    class="select-menu range flex items-center pl-2 pr-2" />
                  <span>%</span>
                </div>
                <div class="flex items-center gap-3">
                  <span class="font-medium">Test</span>
                  <input
                    type="number"
                    formControlName="test_size"
                    max="95"
                    min="5"
                    [(ngModel)]="test_size"
                    class="select-menu range flex items-center pl-2 pr-2" />
                  <span>%</span>
                </div>
              </div>
              <div
                class="text-red-500 text-sm mt-2"
                *ngIf="
                  trainingForm.get('train_size')?.errors ||
                  trainingForm.get('test_size')?.errors
                ">
                Must be in range between 5 and 95
              </div>
            </div>
          </div>

          <div class="flex justify-between items-center dialog-actions">
            <button mat-button matStepperPrevious>Back</button>
            <span class="text-sm text-gray-600">Step {{ stepper.selectedIndex + 1 }} of 3</span>
            <button
              mat-flat-button
              matStepperNext
              [disabled]="trainingForm.invalid">
              Next
            </button>
          </div>
        </form>
      </mat-step>
      <mat-step>
        <div class="form-wrapper">
          <div class="flex-grow">
            <app-training-preview
              [trainingDetails]="getTrainingsDetails()"></app-training-preview>
          </div>

          <div class="flex justify-between items-center dialog-actions">
            <button mat-button matStepperPrevious>Back</button>
            <span class="text-sm text-gray-600">Step {{ stepper.selectedIndex + 1 }} of 3</span>
            <button
              mat-flat-button
              matStepperNext
              [disabled]="trainingForm.invalid"
              (click)="createTraining($event)">
              {{ stepper.selectedIndex === 2 ? 'Create Training' : 'Next' }}
            </button>
          </div>
        </div>
      </mat-step>
    </mat-stepper>
  </mat-dialog-content>
</div>
