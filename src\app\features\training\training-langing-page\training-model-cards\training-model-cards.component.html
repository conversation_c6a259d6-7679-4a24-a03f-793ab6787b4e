<div class="card-wrapper h-[68px] flex items-center justify-between">
  <div class="flex items-center">
    <div class="menu ml-2">
      <button mat-icon-button [matMenuTriggerFor]="menu">
        <mat-icon>more_vert</mat-icon>
      </button>
      <mat-menu #menu="matMenu">
        <button mat-menu-item (click)="tryAnotherModel(projectInfo.id)">
          <mat-icon class="fill-none">fast_forward</mat-icon>
          <span>Try another model</span>
        </button>
        <button mat-menu-item (click)="stopTrainingModel()">
          <mat-icon>remove_circle_outline</mat-icon>
          <span>Stop Model</span>
        </button>
      </mat-menu>
    </div>
    <div class="title ml-2">
      <div class="heading font-medium text-lg">{{ projectInfo.name }}</div>
      <div class="status text-sm">{{ projectInfo.file.file_name }}</div>
    </div>
  </div>

  <div
    class="flex items-center mr-2"
    tabindex="0"
    (keydown)="handleStatusKeydown($event, projectInfo.status, projectInfo.id)">
    <button
      [class]="projectInfo.status"
      (click)="onStatusClick(projectInfo.status, projectInfo.id)">
      {{ getTrainingStatus(projectInfo.status).title }}
    </button>
    <button
      mat-icon-button
      aria-label="Example icon button with a vertical three dot icon">
      <mat-icon (click)="onStatusClick(projectInfo.status, projectInfo.id)">{{
        getTrainingStatus(projectInfo.status).icon
      }}</mat-icon>
    </button>
  </div>
</div>
