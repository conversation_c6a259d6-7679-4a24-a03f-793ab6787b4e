.dialog-wrapper {
  padding: 32px;
  height: 100%;
  box-sizing: border-box;
}

.form-input {
  padding: 14px;
  border-radius: 8px;
  border: 1px solid #ccc;
  box-sizing: border-box;
  line-height: 1.5;
  font-size: 14px;
}

::ng-deep .mat-mdc-dialog-container {
  width: 860px;
  height: 580px;
  max-width: 860px;
  max-height: 580px;
}

::ng-deep .mat-horizontal-content-container {
  padding: 0px !important;
  height: 100%;
}

::ng-deep .mat-mdc-dialog-content {
  padding: 0px !important;
  height: calc(100% - 80px);
  overflow-y: auto;
}

::ng-deep .mat-mdc-select-panel {
  margin-left: -10px;
}

.select-menu {
  background-color: white;
  height: 52px;
  border: 1px solid #ccc;
  border-radius: 8px;
  font-size: 14px;
}

.range {
  width: 140px !important;
}

.form-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 450px;
}

.flex-grow {
  flex-grow: 1;
  padding-bottom: 20px;
}

.dialog-actions {
  margin-top: auto;
  padding-top: 20px;
  border-top: 1px solid #e0e0e0;
}

::ng-deep .mat-horizontal-stepper-header {
  display: none !important;
}

::ng-deep .mat-stepper-horizontal-line {
  display: none;
}
